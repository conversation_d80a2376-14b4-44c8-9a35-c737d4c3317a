<template>
  <div class="products-view">
    <div class="page-header">
      <h1>商品管理</h1>
      <p>管理您的商品信息、库存和价格</p>
    </div>

    <el-card class="content-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>商品列表</span>
          <el-button type="primary" :icon="Plus">添加商品</el-button>
        </div>
      </template>

      <div class="development-notice">
        <el-icon class="notice-icon"><InfoFilled /></el-icon>
        <div class="notice-content">
          <h3>功能开发中</h3>
          <p>商品管理功能正在开发中，敬请期待！</p>
          <p>即将支持的功能：</p>
          <ul>
            <li>商品信息管理</li>
            <li>库存管理</li>
            <li>价格管理</li>
            <li>商品分类</li>
            <li>批量操作</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus, InfoFilled } from '@element-plus/icons-vue'

// 商品管理页面
</script>

<style scoped>
.products-view {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.content-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.development-notice {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
}

.notice-icon {
  font-size: 24px;
  color: #409eff;
  margin-top: 4px;
}

.notice-content h3 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 8px 0;
}

.notice-content p {
  font-size: 14px;
  color: #606266;
  margin: 0 0 8px 0;
}

.notice-content ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.notice-content li {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}
</style>
